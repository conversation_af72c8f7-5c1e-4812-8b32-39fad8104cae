"use client"

import { useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { Loader2, CheckCircle2, XCircle } from "lucide-react"

interface PaymentModalProps {
  open: boolean
  status: "loading" | "success" | "error" | null
  message?: string
  onClose: () => void
}

export function PaymentModal({ open, status, message, onClose }: PaymentModalProps) {
  useEffect(() => {
    if (status === "success") {
      const timer = setTimeout(() => onClose(), 2500) // auto close after 2.5s
      return () => clearTimeout(timer)
    }
  }, [status, onClose])

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md text-center space-y-4">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold">
            Payment Status
          </DialogTitle>
        </DialogHeader>

        {status === "loading" && (
          <div className="flex flex-col items-center space-y-3">
            <Loader2 className="h-10 w-10 animate-spin text-[#f68c1e]" />
            <p className="text-gray-600">{message || "Please check your phone to finish payment..."}</p>
          </div>
        )}

        {status === "success" && (
          <div className="flex flex-col items-center space-y-3">
            <CheckCircle2 className="h-12 w-12 text-green-600" />
            <p className="text-green-600 font-medium">{message || "Payment successful!"}</p>
          </div>
        )}

        {status === "error" && (
          <div className="flex flex-col items-center space-y-3">
            <XCircle className="h-12 w-12 text-red-500" />
            <p className="text-red-500 font-medium">{message || "Payment failed!"}</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
