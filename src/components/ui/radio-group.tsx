"use client"

import * as React from "react"
import * as RadixRadioGroup from "@radix-ui/react-radio-group"
import { cn } from "@/lib/utils"

const RadioGroup = RadixRadioGroup.Root
const RadioGroupItem = React.forwardRef<
  React.ElementRef<typeof RadixRadioGroup.Item>,
  React.ComponentPropsWithoutRef<typeof RadixRadioGroup.Item>
>(({ className, ...props }, ref) => (
  <RadixRadioGroup.Item
    ref={ref}
    className={cn(
      "h-4 w-4 rounded-full border border-border text-primary ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",
      className
    )}
    {...props}
  />
))

RadioGroupItem.displayName = "RadioGroupItem"

export { RadioGroup, RadioGroupItem }
