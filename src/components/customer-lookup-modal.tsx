import { Button } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"

interface CustomerData {
    name: string
    addressLine1: string
    addressLine2: string
    addressLine3: string
    addressLine4: string
    meterNumber: string
    phoneNumber: string
    meterCurrency: string
}

interface CustomerLookupModalProps {
    isOpen: boolean
    onClose: () => void
    onConfirm: () => void
    customerData: CustomerData | null
}

export function CustomerLookupModal({ isOpen, onClose, onConfirm, customerData }: CustomerLookupModalProps) {
    if (!customerData) return null

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Customer Information</DialogTitle>
                    <DialogDescription>Please confirm the customer details below.</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <span className="font-bold col-span-1">Name:</span>
                        <span className="col-span-3">{customerData.name}</span>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <span className="font-bold col-span-1">Address:</span>
                        <span className="col-span-3">
                            {customerData.addressLine1}
                            {customerData.addressLine2 && <br />}
                            {customerData.addressLine2}
                            {customerData.addressLine3 && <br />}
                            {customerData.addressLine3}
                            {customerData.addressLine4 !== "null" && <br />}
                            {customerData.addressLine4 !== "null" && customerData.addressLine4}
                        </span>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <span className="font-bold col-span-1">Meter Number:</span>
                        <span className="col-span-3">{customerData.meterNumber}</span>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <span className="font-bold col-span-1">Phone:</span>
                        <span className="col-span-3">{customerData.phoneNumber !== "null" ? customerData.phoneNumber : "N/A"}</span>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <span className="font-bold col-span-1">Meter Currency:</span>
                        <span className="col-span-3">{customerData.meterCurrency}</span>
                    </div>
                </div>
                <DialogFooter>
                    <Button onClick={onClose} variant="outline">
                        Cancel
                    </Button>
                    <Button onClick={onConfirm} className="bg-[#f68c1e] hover:bg-[#e07c1a]">
                        Confirm
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

