"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, MapPin, Users, Clock } from "lucide-react"
import { useRouter } from "next/navigation"

// Sample events data - this would typically come from an API
const events = [
  {
    id: 1,
    title: "Harare International Music Festival",
    description: "Join us for an amazing night of music featuring local and international artists.",
    date: "2024-02-15",
    time: "18:00",
    venue: "National Sports Stadium",
    location: "Harare, Zimbabwe",
    price: 25.0,
    currency: "USD",
    availableTickets: 500,
    category: "Music",
    image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=200&fit=crop&crop=center",
  },
  {
    id: 2,
    title: "Harare City Marathon 2024",
    description: "Join thousands of runners in Zimbabwe's premier marathon event through the capital city.",
    date: "2024-04-14",
    time: "06:00",
    venue: "Africa Unity Square",
    location: "Central Harare",
    price: 15.0,
    currency: "USD",
    availableTickets: 2000,
    category: "Sports",
    image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=200&fit=crop&crop=center",
  },
  
 
  
  {
    id: 4,
    title: "Tech Innovation Conference",
    description: "Discover the latest in technology and innovation with industry experts.",
    date: "2024-03-05",
    time: "08:30",
    venue: "University of Zimbabwe",
    location: "Mount Pleasant, Harare",
    price: 30.0,
    currency: "USD",
    availableTickets: 300,
    category: "Technology",
    image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=200&fit=crop&crop=center",
  },
  // {
  //   id: 5,
  //   title: "Cultural Heritage Festival",
  //   description: "Celebrate Zimbabwe's rich cultural heritage with traditional music, dance, and food.",
  //   date: "2024-03-10",
  //   time: "10:00",
  //   venue: "Harare Gardens",
  //   location: "Central Harare",
  //   price: 10.0,
  //   currency: "USD",
  //   availableTickets: 1000,
  //   category: "Culture",
  //   image: "https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=400&h=200&fit=crop&crop=center",
  // },
  // {
  //   id: 6,
  //   title: "Food & Wine Expo",
  //   description: "Taste the best of Zimbabwe's cuisine and wines from local producers.",
  //   date: "2024-03-15",
  //   time: "12:00",
  //   venue: "Harare International Conference Centre",
  //   location: "Harare, Zimbabwe",
  //   price: 20.0,
  //   currency: "USD",
  //   availableTickets: 400,
  //   category: "Food",
  //   image: "https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400&h=200&fit=crop&crop=center",
  // },
]

export function EventsListing() {
  const router = useRouter()
  const [selectedCategory, setSelectedCategory] = useState<string>("All")

  const categories = ["All", "Music", "Sports", "Technology" ]

  const filteredEvents =
    selectedCategory === "All" ? events : events.filter((event) => event.category === selectedCategory)

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const handleBookTicket = (eventId: number) => {
    router.push(`/services/events/${eventId}`)
  }

  return (
    <div className="space-y-6">
      {/* Category Filter */}
      <div className="flex flex-wrap gap-2 justify-center">
        {categories.map((category) => (
          <Button
            key={category}
            variant={selectedCategory === category ? "default" : "outline"}
            onClick={() => setSelectedCategory(category)}
            className={selectedCategory === category ? "bg-[#f68c1e] hover:bg-[#e07c1a]" : ""}
          >
            {category}
          </Button>
        ))}
      </div>

      {/* Events Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredEvents.map((event) => (
          <Card key={event.id} className="hover:shadow-lg transition-shadow duration-300 overflow-hidden">
            <div className="relative">
              <img src={event.image || "/placeholder.svg"} alt={event.title} className="w-full h-48 object-cover" />
              <Badge className="absolute top-2 right-2 bg-[#f68c1e] text-white">{event.category}</Badge>
            </div>

            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold line-clamp-2">{event.title}</CardTitle>
            </CardHeader>

            <CardContent className="space-y-3">
              <p className="text-gray-600 text-sm line-clamp-2">{event.description}</p>

              <div className="space-y-2 text-sm text-gray-500">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(event.date)}</span>
                </div>

                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  <span>{event.time}</span>
                </div>

                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  <span>
                    {event.venue}, {event.location}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span>{event.availableTickets} tickets available</span>
                </div>
              </div>

              <div className="flex items-center justify-between pt-3 border-t">
                <div className="text-lg font-semibold text-[#f68c1e]">
                  ${event.price.toFixed(2)} {event.currency}
                </div>
                <Button onClick={() => handleBookTicket(event.id)} className="bg-[#f68c1e] hover:bg-[#e07c1a]">
                  Buy Ticket
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredEvents.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No events found in this category.</p>
        </div>
      )}
    </div>
  )
}
