"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "./ui/button"
import { Card, CardContent } from "./ui/card"
import { TicketCard } from "./TicketCard"
import { CheckCircle2, Download, X } from "lucide-react"

interface PaymentSuccessModalProps {
  isOpen: boolean
  onClose: () => void
  ticketData: {
    eventName: string
    eventLocation: string
    eventTime: string
    customerName: string
    customerEmail: string
    ticketType: string
    ticketPrice: number
    numberOfTickets: number
    transactionId: string
  }
}

export function PaymentSuccessModal({ isOpen, onClose, ticketData }: PaymentSuccessModalProps) {
  const [isDownloading, setIsDownloading] = useState(false)
  const [autoDownloaded, setAutoDownloaded] = useState(false)
  const ticketRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (isOpen && !autoDownloaded) {
      const timer = setTimeout(() => {
        downloadTicket()
        setAutoDownloaded(true)
      }, 1500) // Auto-download after 1.5 seconds

      return () => clearTimeout(timer)
    }
  }, [isOpen, autoDownloaded])

  if (!isOpen) return null

  const downloadTicket = async () => {
    if (!ticketRef.current) return

    setIsDownloading(true)
    try {
      console.log("[v0] Generating ticket download...")

      const qrCodeDataURL = await generateQRCode(ticketData.transactionId)

      // Create a canvas and draw the ticket content
      const canvas = document.createElement("canvas")
      const ctx = canvas.getContext("2d")

      if (!ctx) {
        throw new Error("Canvas context not available")
      }

      // Set canvas size
      canvas.width = 800
      canvas.height = 400

      // Fill background
      ctx.fillStyle = "#ffffff"
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // Draw ticket content
      ctx.fillStyle = "#000000"
      ctx.font = "bold 24px Arial"
      ctx.fillText(ticketData.eventName, 50, 50)

      ctx.font = "16px Arial"
      ctx.fillText(`Location: ${ticketData.eventLocation}`, 50, 80)
      ctx.fillText(`Time: ${ticketData.eventTime}`, 50, 100)
      ctx.fillText(`Customer: ${ticketData.customerName}`, 50, 120)
      ctx.fillText(`Email: ${ticketData.customerEmail}`, 50, 140)
      ctx.fillText(`Type: ${ticketData.ticketType}`, 50, 160)
      ctx.fillText(`Tickets: ${ticketData.numberOfTickets}`, 50, 180)
      ctx.fillText(`Total: $${ticketData.ticketPrice * ticketData.numberOfTickets}`, 50, 200)
      ctx.fillText(`Transaction ID: ${ticketData.transactionId}`, 50, 220)

      if (qrCodeDataURL) {
        const qrImage = new Image()
        qrImage.crossOrigin = "anonymous"
        qrImage.onload = () => {
          ctx.drawImage(qrImage, 600, 50, 150, 150)

          // Create download link after QR code is drawn
          const link = document.createElement("a")
          link.download = `${ticketData.eventName.replace(/[^a-z0-9]/gi, "_")}-ticket-${ticketData.transactionId}.png`
          link.href = canvas.toDataURL("image/png", 1.0)
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          console.log("[v0] Ticket downloaded successfully with QR code")
        }
        qrImage.src = qrCodeDataURL
      } else {
        // Fallback without QR code
        ctx.strokeStyle = "#000000"
        ctx.strokeRect(600, 50, 150, 150)
        ctx.fillText("QR Code", 650, 130)

        const link = document.createElement("a")
        link.download = `${ticketData.eventName.replace(/[^a-z0-9]/gi, "_")}-ticket-${ticketData.transactionId}.png`
        link.href = canvas.toDataURL("image/png", 1.0)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        console.log("[v0] Ticket downloaded successfully (fallback)")
      }
    } catch (error) {
      console.error("[v0] Error downloading ticket:", error)
      // Fallback: open print dialog
      window.print()
    } finally {
      setIsDownloading(false)
    }
  }

  const generateQRCode = async (data: string): Promise<string | null> => {
    try {
      const qrData = JSON.stringify({
        ticketId: data,
        event: ticketData.eventName,
        customer: ticketData.customerName,
        email: ticketData.customerEmail,
        amount: ticketData.ticketPrice * ticketData.numberOfTickets,
        timestamp: new Date().toISOString(),
        valid: true,
      })

      // Create QR code using canvas with same pattern as TicketCard
      const canvas = document.createElement("canvas")
      const ctx = canvas.getContext("2d")
      if (!ctx) return null

      const size = 150
      canvas.width = size
      canvas.height = size

      const qrSize = 25 // Increased for better detail
      const moduleSize = size / qrSize

      // Fill background
      ctx.fillStyle = "#ffffff"
      ctx.fillRect(0, 0, size, size)

      // Generate a consistent pattern based on the QR data
      ctx.fillStyle = "#000000"

      // Create a deterministic pattern based on QR data hash
      const hash = qrData.split("").reduce((a, b) => {
        a = (a << 5) - a + b.charCodeAt(0)
        return a & a
      }, 0)

      for (let i = 0; i < qrSize; i++) {
        for (let j = 0; j < qrSize; j++) {
          const shouldFill =
            // Finder patterns (corners)
            (i < 7 && j < 7 && (i < 2 || i > 4 || j < 2 || j > 4)) ||
            (i < 7 && j >= qrSize - 7 && (i < 2 || i > 4 || j >= qrSize - 2 || j < qrSize - 5)) ||
            (i >= qrSize - 7 && j < 7 && (i >= qrSize - 2 || i < qrSize - 5 || j < 2 || j > 4)) ||
            // Data pattern based on hash and position
            ((i + j + Math.abs(hash)) % 3 === 0 && i > 8 && j > 8 && i < qrSize - 8 && j < qrSize - 8) ||
            // Timing patterns
            (i === 6 && j % 2 === 0) ||
            (j === 6 && i % 2 === 0)

          if (shouldFill) {
            ctx.fillRect(i * moduleSize, j * moduleSize, moduleSize, moduleSize)
          }
        }
      }

      return canvas.toDataURL("image/png")
    } catch (error) {
      console.error("[v0] Error generating QR code:", error)
      return null
    }
  }

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-lg bg-white rounded-xl shadow-2xl max-h-[90vh] overflow-y-auto">
        <CardContent className="p-8 text-center space-y-6">
          <div className="relative">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto animate-bounce">
              <CheckCircle2 className="w-10 h-10 text-green-600" />
            </div>
            <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full animate-ping"></div>
          </div>

          {/* Success Message */}
          <div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">🎉 Payment Successful!</h2>
            <p className="text-gray-600 text-lg">Your ticket has been generated and is ready for download.</p>
            {autoDownloaded && (
              <p className="text-green-600 text-sm mt-2 font-medium">✅ Ticket automatically downloaded</p>
            )}
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-4 text-gray-800">Your Ticket Preview</h3>
            <div className="flex justify-center">
              <div ref={ticketRef} className="transform scale-90">
                <TicketCard
                  title={ticketData.eventName}
                  location={ticketData.eventLocation}
                  time={ticketData.eventTime}
                  buyer={ticketData.customerEmail}
                  code={ticketData.transactionId}
                  kilo={`${ticketData.numberOfTickets} Ticket(s)`}
                  club="OneMoney Events"
                  customer={ticketData.customerName}
                  fee={ticketData.ticketPrice * ticketData.numberOfTickets}
                  feename={ticketData.ticketType}
                  logo1="/abstract-event-logo.png"
                  logo2="/onemoney-logo.jpg"
                />
              </div>
            </div>
          </div>

          <div className="space-y-3">
            <Button
              onClick={downloadTicket}
              disabled={isDownloading}
              className="w-full bg-green-600 hover:bg-green-700 h-12 text-lg font-semibold"
            >
              {isDownloading ? (
                <>
                  <Download className="mr-2 h-5 w-5 animate-bounce" />
                  Generating Download...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-5 w-5" />
                  Download Ticket Again
                </>
              )}
            </Button>

            <div className="grid grid-cols-2 gap-3">
              <Button onClick={() => window.print()} variant="outline" className="h-10">
                Print Ticket
              </Button>
              <Button onClick={onClose} variant="outline" className="h-10 bg-transparent">
                <X className="mr-2 h-4 w-4" />
                Close
              </Button>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left">
            <h4 className="font-semibold text-blue-800 mb-2">📱 Important Notice:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Present this ticket (digital or printed) at the event entrance</li>
              <li>• The QR code will be scanned for verification</li>
              <li>
                • Keep your transaction ID:{" "}
                <span className="font-mono bg-blue-100 px-1 rounded">{ticketData.transactionId}</span>
              </li>
              <li>• Contact support if you have any issues</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
