"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { <PERSON>ton } from "@/components/ui/button"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { PaymentModal } from "./payment-modal"
import { PinModal } from "./pin-modal"
import { OtpModal } from "./otp-modal"
import { TicketConfirmationModal } from "./ticket-confirmation-modal"
import EventsService from "@/services/Events"
import encryptString from "@/utils/encrypt"

const formSchema = z.object({
  mobileNumber: z
    .string()
    .nonempty("Mobile number is required")
    .regex(/^7[1-9]\d{7}$/, "Invalid mobile number format"),
  fullName: z.string().nonempty("Full name is required").min(2, "Name must be at least 2 characters"),
  email: z.string().email("Invalid email address").nonempty("Email is required"),
  ticketQuantity: z.number().min(1, "At least 1 ticket required").max(10, "Maximum 10 tickets per booking"),
  currency: z.enum(["USD", "ZWG"]),
})

interface Event {
  id: number
  title: string
  price: number
  currency: string
  availableTickets: number
}

interface EventBookingFormProps {
  event: Event
}

interface BookingData {
  eventTitle: string
  customerName: string
  email: string
  ticketQuantity: number
  totalAmount: number
  currency: string
}

export function EventBookingForm({ event }: EventBookingFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [pinModalOpen, setPinModalOpen] = useState(false)
  const [otpModalOpen, setOtpModalOpen] = useState(false)
  const [paymentModalOpen, setPaymentModalOpen] = useState(false)
  const [confirmationModalOpen, setConfirmationModalOpen] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [modalMessage, setModalMessage] = useState("")
  const [bookingData, setBookingData] = useState<BookingData | null>(null)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      mobileNumber: "",
      fullName: "",
      email: "",
      ticketQuantity: 1,
      currency: "USD",
    },
  })

  const watchTicketQuantity = form.watch("ticketQuantity")
  const totalAmount = watchTicketQuantity * event.price

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    // Create booking data for confirmation
    const booking: BookingData = {
      eventTitle: event.title,
      customerName: values.fullName,
      email: values.email,
      ticketQuantity: values.ticketQuantity,
      totalAmount: totalAmount,
      currency: values.currency,
    }

    setBookingData(booking)
    setConfirmationModalOpen(true)
  }

  const handleBookingConfirm = () => {
    setConfirmationModalOpen(false)
    setPinModalOpen(true)
  }

  function handlePinSubmit(pin: string) {
    setIsSubmitting(true)
    setPinModalOpen(false)

    const data = {
      timestamp: Math.floor(Date.now() / 1000),
      random: "UUID",
      identifierType: "1",
      identifier: form.getValues("mobileNumber"),
      securityCredential: pin,
      eventId: event.id,
      customerName: form.getValues("fullName"),
      email: form.getValues("email"),
      ticketQuantity: form.getValues("ticketQuantity"),
      currency: form.getValues("currency"),
      totalAmount: totalAmount,
    }

    encryptString(JSON.stringify(data))
      .then((encrypted) => {
        return EventsService.pay({
          payload: encrypted,
        })
      })
      .then((result) => {
        const success = result.data.success

        if (success) {
          setIsSuccess(success)
          setOtpModalOpen(true)
        } else {
          setIsSuccess(false)
          setModalMessage("Payment initiation failed. Please try again.")
          setPaymentModalOpen(true)
        }
      })
      .catch((error) => {
        console.log(error)
        setIsSuccess(false)
        setModalMessage("Payment failed. Please check your details and try again.")
        setPaymentModalOpen(true)
      })
      .finally(() => {
        setIsSubmitting(false)
      })
  }

  function handleOtpSubmit(otp: string) {
    setIsSubmitting(true)
    setOtpModalOpen(false)

    const data = {
      otp: otp,
    }

    EventsService.confirm(data)
      .then((result) => {
        const confirmed = result.data.success

        if (confirmed) {
          setIsSuccess(confirmed)
          setModalMessage(
            `Successfully booked ${form.getValues("ticketQuantity")} ticket(s) for ${event.title}. Total: $${totalAmount.toFixed(2)} ${form.getValues("currency")}. Tickets will be sent to ${form.getValues("email")}.`,
          )
          setPaymentModalOpen(true)
          form.reset()
        } else {
          setIsSuccess(false)
          setModalMessage("Booking confirmation failed. Please contact support.")
          setPaymentModalOpen(true)
        }
      })
      .catch((error) => {
        console.log(error)
        setIsSuccess(false)
        setModalMessage("Booking confirmation failed. Please try again later.")
        setPaymentModalOpen(true)
      })
      .finally(() => {
        setIsSubmitting(false)
      })
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="fullName"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-gray-500">Full Name</FormLabel>
                <FormControl className="text-gray-500 rounded-xl h-[50px]">
                  <Input className="text-gray-500" placeholder="Enter your full name" {...field} />
                </FormControl>
                <FormMessage className="text-red-500" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-gray-500">Email Address</FormLabel>
                <FormControl className="text-gray-500 rounded-xl h-[50px]">
                  <Input className="text-gray-500" placeholder="<EMAIL>" type="email" {...field} />
                </FormControl>
                <FormDescription className="text-gray-500">Tickets will be sent to this email address</FormDescription>
                <FormMessage className="text-red-500" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="mobileNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-gray-500">Mobile Number</FormLabel>
                <FormControl className="text-gray-500 rounded-xl h-[50px]">
                  <Input className="text-gray-500" placeholder="710000000" {...field} />
                </FormControl>
                <FormDescription className="text-gray-500">Enter your mobile number (e.g., 710000000)</FormDescription>
                <FormMessage className="text-red-500" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="ticketQuantity"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-gray-500">Number of Tickets</FormLabel>
                <Select
                  onValueChange={(value) => field.onChange(Number.parseInt(value))}
                  defaultValue={field.value.toString()}
                >
                  <FormControl className="text-gray-500 rounded-xl h-[50px]">
                    <SelectTrigger>
                      <SelectValue placeholder="Select quantity" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="text-gray-500 bg-gray-100">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                      <SelectItem key={num} className="text-gray-500" value={num.toString()}>
                        {num} ticket{num > 1 ? "s" : ""}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage className="text-red-500" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="currency"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-gray-500">Currency</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl className="text-gray-500 rounded-xl h-[50px]">
                    <SelectTrigger>
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent className="text-gray-500 bg-gray-100">
                    <SelectItem className="text-gray-500" value="USD">
                      USD
                    </SelectItem>
                    <SelectItem className="text-gray-500" value="ZWG">
                      ZWG
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage className="text-red-500" />
              </FormItem>
            )}
          />

          {/* Total Amount Display */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Total Amount:</span>
              <span className="text-xl font-bold text-[#f68c1e]">
                ${totalAmount.toFixed(2)} {form.watch("currency")}
              </span>
            </div>
          </div>

          <Button type="submit" className="w-full bg-[#f68c1e] hover:bg-[#e07c1a]" disabled={isSubmitting}>
            {isSubmitting ? "Processing..." : "Book Tickets"}
          </Button>
        </form>
      </Form>

      <TicketConfirmationModal
        isOpen={confirmationModalOpen}
        onClose={() => setConfirmationModalOpen(false)}
        onConfirm={handleBookingConfirm}
        bookingData={bookingData}
      />

      <PinModal isOpen={pinModalOpen} onClose={() => setPinModalOpen(false)} onSubmit={handlePinSubmit} />

      <OtpModal isOpen={otpModalOpen} onClose={() => setOtpModalOpen(false)} onSubmit={handleOtpSubmit} />

      <PaymentModal
        isOpen={paymentModalOpen}
        onClose={() => setPaymentModalOpen(false)}
        isSuccess={isSuccess}
        message={modalMessage}
      />
    </>
  )
}
