import { Shield, Zap, HeadphonesIcon } from "lucide-react"

const features = [
    {
        icon: Shield,
        title: "Secure Transactions",
        description: "Your payments are protected with state-of-the-art encryption technology.",
    },
    {
        icon: Zap,
        title: "Instant Processing",
        description: "Your electricity bill payments are processed immediately, ensuring no late fees.",
    },
    {
        icon: HeadphonesIcon,
        title: "24/7 Support",
        description: "Our customer support team is always available to assist you with any issues.",
    },
]

export default function Features() {
    return (
        <div className="bg-gray-100 py-12">
            <div className="container mx-auto px-4">
                <h2 className="text-3xl font-bold mb-8 text-center">Why Choose Us</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    {features.map((feature, index) => (
                        <div key={index} className="flex flex-col items-center text-center bg-white p-6 rounded-lg shadow-md">
                            <feature.icon className="w-12 h-12 text-[#f68c1e] mb-4" />
                            <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                            <p className="text-gray-600">{feature.description}</p>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    )
}

