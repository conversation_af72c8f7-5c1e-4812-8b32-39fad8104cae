"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Calendar, Users, Mail } from "lucide-react"

interface BookingData {
  eventTitle: string
  customerName: string
  email: string
  ticketQuantity: number
  totalAmount: number
  currency: string
}

interface TicketConfirmationModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  bookingData: BookingData | null
}

export function TicketConfirmationModal({ isOpen, onClose, onConfirm, bookingData }: TicketConfirmationModalProps) {
  if (!bookingData) return null

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl">Confirm Your Booking</DialogTitle>
          <DialogDescription>Please review your ticket booking details below.</DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-lg text-gray-800 mb-3">{bookingData.eventTitle}</h3>

            <div className="space-y-3 text-sm">
              <div className="flex items-center gap-3">
                <Users className="w-4 h-4 text-[#f68c1e]" />
                <span className="font-medium">Customer:</span>
                <span>{bookingData.customerName}</span>
              </div>

              <div className="flex items-center gap-3">
                <Mail className="w-4 h-4 text-[#f68c1e]" />
                <span className="font-medium">Email:</span>
                <span>{bookingData.email}</span>
              </div>

              <div className="flex items-center gap-3">
                <Calendar className="w-4 h-4 text-[#f68c1e]" />
                <span className="font-medium">Tickets:</span>
                <span>
                  {bookingData.ticketQuantity} ticket{bookingData.ticketQuantity > 1 ? "s" : ""}
                </span>
              </div>
            </div>
          </div>

          <div className="bg-[#f68c1e]/10 rounded-lg p-4 border border-[#f68c1e]/20">
            <div className="flex justify-between items-center">
              <span className="font-semibold text-gray-800">Total Amount:</span>
              <span className="text-xl font-bold text-[#f68c1e]">
                ${bookingData.totalAmount.toFixed(2)} {bookingData.currency}
              </span>
            </div>
          </div>

          <div className="text-xs text-gray-500 bg-blue-50 p-3 rounded-lg">
            <p className="font-medium mb-1">Important:</p>
            <ul className="space-y-1">
              <li>• Tickets will be sent to your email address</li>
              <li>• Please arrive 30 minutes before the event starts</li>
              <li>• Bring a valid ID for entry</li>
            </ul>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button onClick={onClose} variant="outline">
            Cancel
          </Button>
          <Button onClick={onConfirm} className="bg-[#f68c1e] hover:bg-[#e07c1a]">
            Confirm & Pay
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
