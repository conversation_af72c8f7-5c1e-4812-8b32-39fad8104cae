
"use client"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useRouter } from "next/navigation"
import electric_icon from "../assets/images/zesa.png"
import airtime from "../assets/images/airtime-removebg-preview.png"
import airDATA from "../assets/images/data-removebg-preview.png"
import harare from "../assets/images/city.png"
import { Label } from "./ui/label"
import Image from "next/image"




const products = [
    {
        name: "Buy Zesa Tokens",
        icon: electric_icon,
        available: true,
        description: "Easily pay your electricity bills online. No more queues or late payments.",
        href: "/services/electricity"
    },
    {
        name: "City of Harare",
        icon: harare,
        available: false,
        description: "Pay your bills, fees and levies anytime and anywhere",
        href: ""
    },
    {
        name: "Buy Airtime",
        icon: airtime,
        available: false,
        description: "Recharge your mobile phone credit instantly, anytime and anywhere.",
        href: ""
    },
    {
        name: "Buy Bundle",
        icon: airDATA,
        available: false,
        description: "Purchase internet data bundles for seamless browsing and streaming.",
        href: ""
    },
    {
        name: "Events",
        icon: airDATA,
        available: true,
        description: "Book tickets for upcoming events and concerts with just a few clicks.",
        href: "services/events"
    },

]

export default function ProductGrid() {
    const router = useRouter()
    return (
        <div className="container mx-auto px-4 py-12" id="products">
            <h2 className="text-3xl font-bold mb-8 text-center">Our Products</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {products.map((product, index) => (
                    <Card
                        key={index}
                        className={`hover:shadow-lg transition-shadow duration-300 ${!product.available ? "opacity-60" : ""}`}
                    >
                        <CardHeader>
                            <CardTitle className="flex items-center justify-between">
                                <span className="flex items-center">
                                    {/* <product.icon className={`w-6 h-6 mr-2 ${product.available ? "text-[#f68c1e]" : "text-gray-400"}`} /> */}
                                    <Image className={`w-10 h-19 mr-2 ${product.available ? "text-[#f68c1e]" : "text-gray-400"}`} src={product.icon} alt={product.name} />
                                    <Label htmlFor="product">

                                        {product.name}
                                    </Label>
                                </span>
                                {!product.available && <Badge className="bg-[#f68c1e] text-white">Coming Soon</Badge>}
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-gray-600 mb-4">{product.description}</p>
                            <Button onClick={() => {
                                router.push(product.href)
                            }} size="lg" className="w-full" disabled={!product.available}>
                                {product.available ? "Pay Now" : "Not Available"}
                            </Button>
                        </CardContent>
                    </Card>
                ))}
            </div>
        </div>
    )
}

