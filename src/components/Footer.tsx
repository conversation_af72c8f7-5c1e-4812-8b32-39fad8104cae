import Link from "next/link"
import { AppleIcon, PlayIcon } from "lucide-react"

export default function Footer() {
    return (
        <footer className="bg-gray-800 text-white py-8">
            <div className="container mx-auto px-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div>
                        <h3 className="text-2xl font-bold mb-4">Online Digital Products</h3>
                        <p className="text-sm">Your trusted digital utility provider</p>
                    </div>
                    <div>
                        <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
                        <ul className="space-y-2">
                            <li>
                                <Link href="https://onemoney.co.zw/" className="hover:text-[#f68c1e] transition-colors duration-300">
                                    Netone Website
                                </Link>
                                 
                            </li>
                            <li>
                                <Link href="https://onemoney.co.zw/" className="hover:text-[#f68c1e] transition-colors duration-300">
                                    OneMoney Website
                                </Link>
                            </li>

                        </ul>
                    </div>
                    <div>
                        <h4 className="text-lg font-semibold mb-4">Download Our OneMoney Mobile App</h4>
                        <div className="flex flex-col space-y-2">
                            <Link
                                href="https://apps.apple.com/zw/app/onemoney-mobile/id6737691641"
                                className="flex items-center hover:text-[#f68c1e] transition-colors duration-300"
                            >
                                <AppleIcon className="mr-2 h-5 w-5" /> App Store
                            </Link>
                            <Link
                                href="https://play.google.com/store/apps/details?id=zw.co.onemoney.mob&pcampaignid=web_share"
                                className="flex items-center hover:text-[#f68c1e] transition-colors duration-300"
                            >
                                <PlayIcon className="mr-2 h-5 w-5" /> Google Play
                            </Link>
                        </div>
                    </div>
                </div>
                <div className="mt-8 text-center text-sm text-gray-400">
                    © {new Date().getFullYear()} Online Digital Products. All rights reserved.
                </div>
            </div>
        </footer>
    )
}

