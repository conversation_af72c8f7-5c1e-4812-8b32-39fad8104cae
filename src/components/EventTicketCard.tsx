"use client";

import { forwardRef } from "react";
import QRCode from "react-qr-code";
import { Card, CardContent } from "./ui/card";
import {
  Calendar,
  MapPin,
  Clock,
  User,
  Mail,
  Ticket as TicketIcon,
} from "lucide-react";

interface EventTicketCardProps {
  ticket: {
    ticketId: string;
    ownerName: string;
    ownerEmail: string;
    active: boolean;
    ticketStatus: string;
    price: {
      id: string;
      name: string;
      eventId: string;
      currency: string;
      amount: number;
      active: boolean;
    };
    extraDetails: any;
    options: any;
    eventName: string;
    eventLocation: string;
  };
  event?: {
    id: string;
    name: string;
    description: string;
    date: string;
    startTime: string;
    endTime: string;
    location: string;
  };
  transactionId: string;
  onDownload?: () => void;
}

export const EventTicketCard = forwardRef<HTMLDivElement, EventTicketCardProps>(
  ({ ticket, event, transactionId, onDownload }, ref) => {
    const qrData = JSON.stringify({
      transactionId: transactionId,
      ticketId: ticket.ticketId,
    });

    const formatDate = (dateString: string) => {
      if (!dateString) return "TBD";
      try {
        return new Date(dateString).toLocaleDateString("en-US", {
          weekday: "long",
          year: "numeric",
          month: "long",
          day: "numeric",
        });
      } catch {
        return dateString;
      }
    };

    const formatTime = (timeString: string) => {
      if (!timeString) return "TBD";
      return timeString;
    };

    return (
      <Card
        ref={ref}
        className="w-full max-w-2xl rounded-2xl shadow-xl relative overflow-hidden border-2 border-[#f68c1e]/20 bg-gradient-to-br from-yellow-400 via-orange-400 to-yellow-500">
        <CardContent className="p-0">
          {/* Main Ticket Body */}
          <div className="bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-500 p-6 text-white relative">
            {/* Header */}
            <div className="flex justify-between items-start mb-4">
              <div className="flex-1">
                <h1 className="text-2xl font-bold uppercase tracking-wider mb-1">
                  EVENT TICKET
                </h1>
                <p className="text-sm opacity-90">OneMoney Events</p>
              </div>
              <div className="text-right">
                <p className="text-xs opacity-75">ADMIT ONE</p>
                <p className="text-lg font-bold">{ticket.price.currency}</p>
              </div>
            </div>

            {/* Event Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <h2 className="text-xl font-bold mb-2 leading-tight">
                  {ticket.eventName}
                </h2>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4" />
                    <span>{ticket.eventLocation}</span>
                  </div>
                  {event?.date && (
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(event.date)}</span>
                    </div>
                  )}
                  {(event?.startTime || event?.endTime) && (
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      <span>
                        {formatTime(event?.startTime || "")} -{" "}
                        {formatTime(event?.endTime || "")}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Ticket Info */}
              <div className="bg-black/20 rounded-lg p-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="opacity-75">SEAT</p>
                    <p className="font-bold text-lg">A9</p>
                  </div>
                  <div>
                    <p className="opacity-75">ROW</p>
                    <p className="font-bold text-lg">22</p>
                  </div>
                  <div>
                    <p className="opacity-75">SEC</p>
                    <p className="font-bold text-lg">50</p>
                  </div>
                  <div>
                    <p className="opacity-75">PRICE</p>
                    <p className="font-bold text-lg">
                      ${ticket.price.amount.toFixed(2)}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
          </div>

          {/* Stub Section */}
          <div className="bg-white p-6 border-t-2 border-dashed border-gray-300">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Customer Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-bold text-[#f68c1e] mb-3">
                  Ticket Holder Details
                </h3>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-[#f68c1e]" />
                    <span className="font-semibold">Name:</span>
                    <span>{ticket.ownerName}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4 text-[#f68c1e]" />
                    <span className="font-semibold">Email:</span>
                    <span className="text-xs break-all">
                      {ticket.ownerEmail}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <TicketIcon className="w-4 h-4 text-[#f68c1e]" />
                    <span className="font-semibold">Type:</span>
                    <span>{ticket.price.name}</span>
                  </div>
                </div>

                <div className="bg-[#f68c1e]/10 rounded-lg p-3 border border-[#f68c1e]/20">
                  <p className="text-xs text-gray-600 mb-1">Transaction ID</p>
                  <p className="font-mono text-sm font-bold text-[#f68c1e]">
                    {transactionId}
                  </p>
                </div>
              </div>

              {/* QR Code */}
              <div className="flex flex-col items-center justify-center">
                <div className="bg-white p-4 rounded-lg shadow-inner border-2 border-gray-200">
                  <QRCode
                    value={qrData}
                    size={120}
                    style={{ height: "auto", maxWidth: "100%", width: "100%" }}
                    viewBox="0 0 120 120"
                  />
                </div>
                <p className="text-xs text-gray-500 mt-2 text-center">
                  Scan for entry verification
                </p>
                <p className="text-xs text-gray-400 font-mono mt-1">
                  ID: {ticket.ticketId}
                </p>
              </div>
            </div>

            {/* Footer */}
            <div className="mt-6 pt-4 border-t border-gray-200 text-center">
              <p className="text-xs text-gray-500">
                🔒 Secure Digital Ticket • Valid for Single Entry • Present at
                venue entrance
              </p>
              <p className="text-xs text-gray-400 mt-1">
                Generated: {new Date().toLocaleDateString()} • Status:{" "}
                {ticket.ticketStatus}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
);

EventTicketCard.displayName = "EventTicketCard";
