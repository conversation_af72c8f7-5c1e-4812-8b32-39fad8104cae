"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import encryptString from "@/utils/encrypt";
import Zesa from "@/services/Zesa";
import { PaymentModal } from "./payment-modal";
import { PinModal } from "./pin-modal";
import { OtpModal } from "./otp-modal";
import { CustomerLookupModal } from "./customer-lookup-modal";


const formSchema = z.object({
    mobileNumber: z
        .string()
        .nonempty("Mobile number is required")
        .regex(/^7[1-9]\d{7}$/, "Invalid mobile number format"),
    meterNumber: z
        .string()
        .nonempty("Meter number is required")
        .min(1, "Meter number is required"),
    currency: z.enum(["ZWG", "USD"]),
    amount: z
        .number()
        .positive("Amount must be positive")
        .multipleOf(0.01, "Amount can have up to 2 decimal places"),
});

interface CustomerData {
    name: string;
    addressLine1: string;
    addressLine2: string;
    addressLine3: string;
    addressLine4: string;
    meterNumber: string;
    phoneNumber: string;
    meterCurrency: string;
}

export function ElectricityForm() {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isLookingUp, setIsLookingUp] = useState(false);
    const [pinModalOpen, setPinModalOpen] = useState(false);
    const [otpModalOpen, setOtpModalOpen] = useState(false);
    const [paymentModalOpen, setPaymentModalOpen] = useState(false);
    const [isSuccess, setIsSuccess] = useState(false);
    const [modalMessage, setModalMessage] = useState("");
    const [amountInput, setAmountInput] = useState("");
    const [customerLookupModalOpen, setCustomerLookupModalOpen] = useState(false);
    const [customerData, setCustomerData] = useState<CustomerData | null>(null);


    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            mobileNumber: "",
            meterNumber: "",
            currency: "ZWG",
            amount: 0,
        },
    });

    const onSubmit = (values: z.infer<typeof formSchema>) => {
        setIsLookingUp(true);
        Zesa.lookUpCustomer({
            meterNumber: values.meterNumber,
        })
            .then((result) => {
                const customerData = result.data.data;

                if (customerData) {
                    setCustomerData(customerData);
                    setCustomerLookupModalOpen(true);
                } else {
                    setModalMessage(
                        "Customer not found. Please check the meter number and try again."
                    );
                    setPaymentModalOpen(true);
                }
            })
            .catch((error) => {
                console.log(error);
            })
            .finally(() => {
                setIsLookingUp(false);
            });
    };

    const handleCustomerConfirm = () => {
        setCustomerLookupModalOpen(false);
        setPinModalOpen(true);

    };


    function handlePinSubmit(pin: string) {
        setIsSubmitting(true);
        setPinModalOpen(false)
      

         

        const data = {
            timestamp: Math.floor(Date.now() / 1000),
            random: "UUID",
            identifierType: "1",
            identifier: form.getValues("mobileNumber"),
            securityCredential: pin,
            meterNo: form.getValues("meterNumber"),
            currency: form.getValues("currency"),
            topUpAmt: form.getValues("amount"),
        };

        encryptString(JSON.stringify(data)).then((encrypted) => {
            Zesa.pay({
                payload: encrypted,
            })
                .then((result) => {
                    const success = result.data.success;

                    if (success) {
                        setIsSuccess(success);
                       
                        setOtpModalOpen(true)
                    }
                 
                })
                .catch((error) => {
                    console.log(error);
                    setIsSuccess(false);
                  
                })
                .finally(() => {
                    setIsSubmitting(false);
                });
        });
    }

    function handleOtpSubmit(otp: string){
        setIsSubmitting(true)
        setOtpModalOpen(false)

        const data = {
           
            otp: otp
        };

        Zesa.confirm(data).then((result) => {
            const confirmed = result.data.success;

            if (confirmed) {
                setIsSuccess(confirmed);
                setModalMessage(
                    confirmed
                        ? `Successfully paid ${form.getValues(
                            "amount"
                        )} ${form.getValues("currency")} for meter ${form.getValues(
                            "meterNumber"
                        )}`
                        : "Payment failed. Please try again later."
                );
                setPaymentModalOpen(true);
            }
            form.reset();
            setAmountInput("");
        })
        .catch((error) => {
            console.log(error);
            setIsSuccess(false);
            setModalMessage("Payment failed. Please try again later.");
            setPaymentModalOpen(true);
        })
        .finally(() => {
            setIsSubmitting(false);
        });
    }


   

    const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;

        // Allow empty input
        if (value === "") {
            setAmountInput("");
            form.setValue("amount", 0);
            return;
        }

        // Regex to match numbers with up to 2 decimal places
        const regex = /^\d*\.?\d{0,2}$/;
        if (regex.test(value)) {
            setAmountInput(value);
            form.setValue("amount", Number.parseFloat(value) || 0);
        }
    };

    return (
        <>
            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                    <FormField
                        control={form.control}
                        name="mobileNumber"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel className="text-gray-500">Mobile Number</FormLabel>
                                <FormControl className="text-gray-500 rounded-xl h-[50px]">
                                    <Input
                                        className="text-gray-500"
                                        placeholder="710000000"
                                        {...field}
                                    />
                                </FormControl>
                                <FormDescription className="text-gray-500">
                                    Enter your mobile number (e.g., 710000000)
                                </FormDescription>
                                <FormMessage className="text-red-500" />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="meterNumber"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel className="text-gray-500">
                                    Electricity Meter Number
                                </FormLabel>
                                <FormControl className="text-gray-500 rounded-xl h-[50px]">
                                    <Input
                                        className="text-gray-500 "
                                        placeholder="Enter meter number"
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage className="text-red-500" />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="currency"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel className="text-gray-500">Currency</FormLabel>
                                <Select
                                    onValueChange={field.onChange}
                                    defaultValue={field.value}>
                                    <FormControl className="text-gray-500  rounded-xl h-[50px]">
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select currency" />
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent className="text-gray-500 bg-gray-100">
                                        <SelectItem className="text-gray-500" value="ZWG">
                                            ZWG
                                        </SelectItem>
                                        <SelectItem className="text-gray-500" value="USD">
                                            USD
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                                <FormMessage className="text-red-500" />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="amount"
                        render={({ }) => (
                            <FormItem>
                                <FormLabel className="text-gray-500">Amount</FormLabel>
                                <FormControl className="text-gray-500 rounded-xl h-[50px]">
                                    <Input
                                        type="text"
                                        inputMode="decimal"
                                        placeholder="0.00"
                                        value={amountInput}
                                        onChange={handleAmountChange}
                                    />
                                </FormControl>
                                <FormDescription className="text-gray-500"></FormDescription>
                                <FormMessage className="text-red-500" />
                            </FormItem>
                        )}
                    />
                    <Button
                        type="submit"
                        className="w-full bg-[#f68c1e] hover:bg-[#e07c1a]"
                        disabled={isSubmitting || isLookingUp}>
                        {isSubmitting
                            ? "Processing..."
                            : isLookingUp
                                ? "Looking up customer..."
                                : "Submit Payment"}
                    </Button>
                </form>
            </Form>
            <CustomerLookupModal
                isOpen={customerLookupModalOpen}
                onClose={() => setCustomerLookupModalOpen(false)}
                onConfirm={handleCustomerConfirm}
                customerData={customerData}
            />
            <PinModal
                isOpen={pinModalOpen}
                onClose={() => setPinModalOpen(false)}
                 onSubmit={handlePinSubmit}
            />
            <OtpModal
            isOpen={otpModalOpen}
            onClose={()=> setOtpModalOpen(false)}
            onSubmit={handleOtpSubmit}
            />
            <PaymentModal
                isOpen={paymentModalOpen}
                onClose={() => setPaymentModalOpen(false)}
                isSuccess={isSuccess}
                message={modalMessage}
            />
        </>
    );
}