import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface OtpModalProps {
    isOpen: boolean
    onClose: () => void
    onSubmit: (otp: string) => void
}

export function OtpModal({ isOpen, onClose, onSubmit }: OtpModalProps) {
    const [otp, setOtp] = useState('')

    const handleSubmit = () => {
        onSubmit(otp)
        setOtp('')
    }

    return (
        <Dialog open={isOpen}
            onOpenChange={() => {
                setOtp("")
                onClose()
            }}>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Confirm Otp</DialogTitle>
                    <DialogDescription>
                    Please check your phone for an SMS with a 4-digit OTP.
                    Enter it below to confirm your payment.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="otp" className="text-right">
                            Enter OTP
                        </Label>
                        <Input
                            id="otp"
                            type="number"
                            value={otp}
                            onChange={(e) => setOtp(e.target.value)}
                            className="col-span-3"
                            maxLength={4}
                            placeholder=" "
                        />
                    </div>
                </div>
                <DialogFooter>
                    <Button type="submit" onClick={handleSubmit} disabled={otp.length !== 4}>
                        Confirm
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
