import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface PinModalProps {
    isOpen: boolean
    onClose: () => void
    onSubmit: (pin: string) => void
}

export function PinModal({ isOpen, onClose, onSubmit }: PinModalProps) {
    const [pin, setPin] = useState('')

    const handleSubmit = () => {
        onSubmit(pin)
        setPin('')
    }

    return (
        <Dialog open={isOpen}
            onOpenChange={() => {
                setPin("")
                onClose()
            }}>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Enter PIN</DialogTitle>
                    <DialogDescription>
                        Please enter your 4-digit PIN to confirm the transaction.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="pin" className="text-right">
                            PIN
                        </Label>
                        <Input
                            id="pin"
                            type="password"
                            value={pin}
                            onChange={(e) => setPin(e.target.value)}
                            className="col-span-3"
                            maxLength={4}
                            placeholder="****"
                        />
                    </div>
                </div>
                <DialogFooter>
                    <Button type="submit" onClick={handleSubmit} disabled={pin.length !== 4}>
                        Confirm
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
