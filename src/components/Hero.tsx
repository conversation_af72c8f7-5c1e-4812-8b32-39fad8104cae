import { But<PERSON> } from "./ui/button"
import { AppleIcon, PlayIcon } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import heroimage from "/src/assets/images/heroimage.png"
import oneMoney from "/src/assets/images/oneMoney.png"

export default function Hero() {
  return (
    <div className="bg-[#f68c1e] text-white">
      <div className="container mx-auto px-4 py-8 sm:py-12 lg:py-16">
        <div className="flex flex-col lg:flex-row items-center justify-between">
          <div className="hidden sm:block lg:w-1/2 mb-8 lg:mb-0">
            <div className="w-[280] h-[300]  rounded-full flex items-center justify-center mx-auto mb-4">
              <Image
                src={oneMoney || "/placeholder.svg"}
                alt="Online Digital Products Logo"
                width={280}
                height={280}
                className=""
              />
            </div>
            <h1 className="text-4xl font-bold mb-4 sm:text-5xl lg:text-6xl">Online Digital Products</h1>
            <p className="text-xl mb-6 max-w-2xl">
              Your one-stop shop for digital utilities. Pay your electricity bills with ease!
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button asChild variant="secondary" size="lg">
                <Link href="https://onemoney.co.zw/" target="_blank">
                  Visit Main Website
                </Link>
              </Button>
            </div>
          </div>

          {/* mobile */}

          <div className="block sm:hidden">
            <div className="flex justify-between items-center space-x-6">
              <div className="rounded-full flex items-center justify-center  mb-4">
                <Image
                  src={oneMoney || "/placeholder.svg"}
                  alt="Online Digital Products Logo"
                  width={150}
                  height={150}
                  className=""
                />
              </div>
              <h1 className="text-2xl font-bold mb-4">Online Digital Products</h1>
            </div>
            <p className="text-xl mb-6 max-w-2xl">
              Your one-stop shop for digital utilities. Pay your electricity bills with ease!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 mt-12">
              <div className="w-[280] h-[150]  rounded-full flex items-center justify-center mx-auto mb-4">
                <Image
                  src={heroimage || "/placeholder.svg"}
                  alt="Online Digital Products Logo"
                  width={150}
                  height={150}
                  className=""
                />
              </div>
              <div className="flex flex-row sm:flex-row gap-2">
                <Button asChild variant="secondary" size="lg" className="w-full sm:w-auto">
                  <Link href="https://apps.apple.com/zw/app/onemoney-mobile/id6737691641">
                    <AppleIcon className="mr-2 h-5 w-5" /> App Store
                  </Link>
                </Button>
                <Button asChild variant="secondary" size="lg" className="w-full sm:w-auto">
                  <Link href="https://play.google.com/store/apps/details?id=zw.co.onemoney.mob&pcampaignid=web_share">
                    <PlayIcon className="mr-2 h-5 w-5" /> Google Play
                  </Link>
                </Button>
              </div>
              <h2 className="text-xl font-semibold text-center">Download OneMoney Mobile</h2>
            </div>
          </div>

          <div className=" hidden lg:block lg:w-1/2 bg-gray-100 text-black p-6 rounded-lg shadow-lg w-full flex-col items-center justify-center">
            <h2 className="text-2xl font-bold mb-4 text-center">Download Our OneMoney Mobile App</h2>
            <p className="hidden sm:block mb-4 text-center">Manage your utilities on-the-go with our mobile app!</p>
            <div className="w-full flex justify-center item-center mb-4  ">
              <Image
                src={heroimage || "/placeholder.svg"}
                alt="Online Digital Products Logo"
                width={280}
                height={280}
                className=""
              />
            </div>
            <div className="flex flex-col sm:flex-row gap-4 w-full justify-center">
              <Button asChild variant="outline" size="lg" className="w-full sm:w-auto bg-transparent">
                <Link href="https://apps.apple.com/zw/app/onemoney-mobile/id6737691641">
                  <AppleIcon className="mr-2 h-5 w-5" /> App Store
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="w-full sm:w-auto bg-transparent">
                <Link href="https://play.google.com/store/apps/details?id=zw.co.onemoney.mob&pcampaignid=web_share">
                  <PlayIcon className="mr-2 h-5 w-5" /> Google Play
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
