"use client"

import { forwardRef } from "react"
import QRCode from "react-qr-code"
import Image from "next/image"
import { Card, CardContent } from "./ui/card"
import { Separator } from "../components/ui/separator"

interface TicketCardProps {
  title: string
  location: string
  time: string
  buyer: string
  code: string
  kilo: string
  club: string
  customer: string
  fee: number
  feename: string
  logo1: string
  logo2: string
  background?: string
}

export const TicketCard = forwardRef<HTMLDivElement, TicketCardProps>(
  ({ title, location, time, buyer, code, kilo, club, customer, fee, feename, logo1, logo2, background }, ref) => {
    const qrData = JSON.stringify({
      ticketId: code,
      event: title,
      customer: customer,
      email: buyer,
      amount: fee,
      timestamp: new Date().toISOString(),
      valid: true,
    })

    return (
      <Card
        ref={ref}
        className="w-[350px] rounded-2xl shadow-xl relative overflow-hidden border-2 border-[#f68c1e]/20"
        style={{
          backgroundImage: background ? `url(${background})` : "linear-gradient(135deg, #f68c1e10 0%, #ff6b0010 100%)",
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <CardContent className="p-6 space-y-4 bg-white/95 backdrop-blur-sm">
          <div className="flex justify-between items-center">
            <Image
              src={logo1 || "/placeholder.svg?height=50&width=50&query=event logo"}
              alt="Event Logo"
              width={50}
              height={50}
              className="rounded-lg"
            />
            <div className="text-center">
              <p className="text-xs font-semibold text-[#f68c1e] uppercase tracking-wide">Digital Ticket</p>
              <p className="text-xs text-gray-500">OneMoney Events</p>
            </div>
            <Image
              src={logo2 || "/placeholder.svg?height=50&width=50&query=onemoney logo"}
              alt="OneMoney Logo"
              width={50}
              height={50}
              className="rounded-lg"
            />
          </div>

          <div className="flex justify-center items-center flex-col bg-white p-4 rounded-lg shadow-inner">
            <QRCode
              value={qrData}
              size={100}
              style={{ height: "auto", maxWidth: "100%", width: "100%" }}
              viewBox={`0 0 100 100`}
            />
            <p className="text-sm font-bold mt-3 text-[#f68c1e] uppercase tracking-wider">Admit One</p>
            <p className="text-xs text-gray-500 font-mono">ID: {code ? code.slice(-8) : "N/A"}</p>
          </div>

          <Separator className="bg-[#f68c1e]/20" />

          <div className="text-center space-y-2">
            <h2 className="text-xl font-bold text-[#f68c1e] leading-tight">{title}</h2>
            <p className="text-sm font-semibold text-gray-800">{location}</p>
            <div className="bg-[#f68c1e]/10 rounded-lg p-2">
              <p className="text-sm font-bold text-[#f68c1e]">
                {feename}: ${fee ? fee.toFixed(2) : "0.00"}
              </p>
            </div>
            <p className="text-xs text-gray-600 leading-relaxed">{time}</p>
          </div>

          <Separator className="bg-[#f68c1e]/20" />

          <div className="space-y-2">
            <h3 className="text-center text-sm font-bold text-[#f68c1e] uppercase tracking-wide">
              Ticket Holder Details
            </h3>
            <div className="bg-gray-50 rounded-lg p-3 space-y-1">
              <p className="text-sm font-semibold text-gray-800">Name: {customer}</p>
              <p className="text-sm font-semibold text-gray-800">Tickets: {kilo}</p>
              <p className="text-sm font-semibold text-gray-800">Organizer: {club}</p>
              <p className="text-xs text-gray-600 break-all">Email: {buyer}</p>
            </div>
          </div>

          <div className="text-center pt-2 border-t border-[#f68c1e]/20">
            <p className="text-xs text-gray-500">🔒 Secure Digital Ticket • Valid for Single Entry</p>
            <p className="text-xs text-gray-400 font-mono">Generated: {new Date().toLocaleDateString()}</p>
          </div>
        </CardContent>
      </Card>
    )
  },
)

TicketCard.displayName = "TicketCard"
