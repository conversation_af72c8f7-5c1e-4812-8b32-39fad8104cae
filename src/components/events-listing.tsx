"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, MapPin, Users, Clock } from "lucide-react"
import { useRouter } from "next/navigation"

export function EventsListing() {
  const router = useRouter()
  const [selectedCategory, setSelectedCategory] = useState<string>("All")
  const [events, setEvents] = useState<any[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  const categories = ["All", "Music", "Sports", "Technology"]

  useEffect(() => {
    const fetchEvents = async () => {
      setLoading(true)
      setError(null)
      try {
        console.log("[v0] Fetching events from API...")
        const res = await fetch("http://10.95.119.4:8070/eventus/events", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        })

        if (!res.ok) {
          throw new Error(`Failed to fetch events: ${res.status} ${res.statusText}`)
        }

        const data = await res.json()
        console.log("[v0] Events API response:", data)

        // API returns { success, message, body: [...] }
        const eventsList = Array.isArray(data.body) ? data.body : []
        setEvents(eventsList)
        console.log("[v0] Successfully loaded", eventsList.length, "events")
      } catch (err: any) {
        console.error("[v0] Error fetching events:", err)
        setError(err.message || "Error fetching events")
      } finally {
        setLoading(false)
      }
    }
    fetchEvents()
  }, [])

  const filteredEvents =
    selectedCategory === "All" ? events : events.filter((event) => event.eventType === selectedCategory)

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const handleBookTicket = (eventId: string) => {
    console.log("[v0] Navigating to event booking:", eventId)
    router.push(`/services/events/${eventId}`)
  }

  return (
    <div className="space-y-6">
      {/* Category Filter */}
      <div className="flex flex-wrap gap-2 justify-center">
        {categories.map((category) => (
          <Button
            key={category}
            variant={selectedCategory === category ? "default" : "outline"}
            onClick={() => setSelectedCategory(category)}
            className={selectedCategory === category ? "bg-[#f68c1e] hover:bg-[#e07c1a]" : ""}
          >
            {category}
          </Button>
        ))}
      </div>

      {/* Loading/Error */}
      {loading && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#f68c1e] mx-auto mb-4"></div>
          <p className="text-gray-500 text-lg">Loading events...</p>
        </div>
      )}
      {error && (
        <div className="text-center py-12">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <p className="text-red-600 text-lg font-medium">Unable to load events</p>
            <p className="text-red-500 text-sm mt-2">{error}</p>
            <Button onClick={() => window.location.reload()} className="mt-4 bg-red-600 hover:bg-red-700">
              Try Again
            </Button>
          </div>
        </div>
      )}

      {/* Events Grid */}
      {!loading && !error && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredEvents.map((event) => (
              <Card
                key={event.id}
                className="hover:shadow-lg transition-all duration-300 overflow-hidden hover:scale-105"
              >
                <div className="relative">
                  <img
                    src={event.posterUrl || "/placeholder.svg?height=200&width=400&query=event poster"}
                    alt={event.name}
                    className="w-full h-48 object-cover"
                  />
                  {event.eventType && (
                    <Badge className="absolute top-2 right-2 bg-[#f68c1e] text-white">{event.eventType}</Badge>
                  )}
                  <div className="absolute top-2 left-2">
                    <Badge className="bg-green-500 text-white">Available</Badge>
                  </div>
                </div>

                <CardHeader className="pb-3">
                  <CardTitle className="text-lg font-semibold line-clamp-2">{event.name}</CardTitle>
                </CardHeader>

                <CardContent className="space-y-3">
                  <p className="text-gray-600 text-sm line-clamp-2">{event.description}</p>

                  <div className="space-y-2 text-sm text-gray-500">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(event.date)}</span>
                    </div>

                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4" />
                      <span>
                        {event.startTime} - {event.endTime}
                      </span>
                    </div>

                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      <span>{event.location}</span>
                    </div>

                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4" />
                      <span>{event.options?.capacity || "N/A"} seats available</span>
                    </div>
                  </div>

                  {/* Show ALL ticket prices */}
                  <div className="space-y-2 border-t pt-3">
                    {event.prices?.map((price: any) => (
                      <div
                        key={price.id}
                        className="flex items-center justify-between text-sm border p-2 rounded-md bg-gray-50"
                      >
                        <span className="font-medium">{price.name}</span>
                        <span className="font-semibold text-[#f68c1e]">
                          ${price.amount} {price.currency}
                        </span>
                      </div>
                    ))}
                  </div>

                  <div className="flex justify-end pt-3">
                    <Button
                      onClick={() => handleBookTicket(event.id)}
                      className="bg-[#f68c1e] hover:bg-[#e07c1a] transition-colors duration-200"
                    >
                      Buy Ticket
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredEvents.length === 0 && (
            <div className="text-center py-12">
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 max-w-md mx-auto">
                <p className="text-gray-500 text-lg">No events found in this category.</p>
                <Button onClick={() => setSelectedCategory("All")} className="mt-4 bg-[#f68c1e] hover:bg-[#e07c1a]">
                  View All Events
                </Button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  )
}
