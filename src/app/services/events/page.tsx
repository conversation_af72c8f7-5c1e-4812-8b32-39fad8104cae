import { EventsListing } from "@/components/events-listing"
import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import Footer from "@/components/Footer"
import oneMoney from "../../../assets/images/oneMoney.png"
import Image from "next/image"

export default function EventsPage() {
  return (
    <div className="min-h-screen bg-gray-100">
      <header className="bg-gradient-to-r from-[#f68c1e] to-[#ff6b00] text-white p-6 shadow-lg">
        <div className="container mx-auto flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-3">
            <h1 className="text-2xl font-bold tracking-tight">Events & Tickets</h1>
          </Link>
          <Link href="/">
            <Button variant="outline" className="bg-white text-[#f68c1e] hover:bg-gray-100">
              Back to Home
            </Button>
          </Link>
        </div>
      </header>

      <main className="container mx-auto py-12">
        <div className="max-w-6xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
            <div className="bg-gradient-to-br from-[#f68c1e] to-[#ff6b00] p-6">
              <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                <Image src={oneMoney || "/placeholder.svg"} alt="events" className="w-38 h-24" />
              </div>
              <h2 className="text-2xl font-semibold text-center text-white">Available Events</h2>
              <p className="text-center text-white/90 mt-2">Book tickets for upcoming events and concerts</p>
            </div>
          </div>
          <EventsListing />
        </div>
      </main>

      <Footer />
    </div>
  )
}
