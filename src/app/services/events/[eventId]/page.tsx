"use client";

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import {
  <PERSON>,
  CardHeader,
  Card<PERSON><PERSON>le,
  CardContent,
} from "../../../../components/ui/card";
import { Button } from "../../../../components/ui/button";
import { Label } from "../../../../components/ui/label";
import { Input } from "../../../../components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../components/ui/select";
import { CheckCircle2, XCircle, Loader2 } from "lucide-react";
import axios from "axios";

function PaymentModal({
  isOpen,
  status,
  message,
}: {
  isOpen: boolean;
  status: "loading" | "success" | "error" | null;
  message: string;
}) {
  if (!isOpen) return null;

  let bgColor = "bg-blue-50 border-blue-200";
  let textColor = "text-blue-800";
  let icon = <Loader2 className="w-8 h-8 animate-spin text-blue-600" />;

  if (status === "success") {
    bgColor = "bg-green-50 border-green-200";
    textColor = "text-green-800";
    icon = <CheckCircle2 className="w-8 h-8 text-green-600" />;
  } else if (status === "error") {
    bgColor = "bg-red-50 border-red-200";
    textColor = "text-red-800";
    icon = <XCircle className="w-8 h-8 text-red-600" />;
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div
        className={`p-8 rounded-xl shadow-xl ${bgColor} border-2 w-full max-w-md text-center`}>
        <div className="flex justify-center mb-4">{icon}</div>
        <h3 className={`text-lg font-semibold mb-2 ${textColor}`}>
          {status === "loading" && "Processing Payment"}
          {status === "success" && "Payment Successful!"}
          {status === "error" && "Payment Failed"}
        </h3>
        <p className={`${textColor} break-words leading-relaxed`}>{message}</p>
        {status === "loading" && (
          <div className="mt-4">
            <div className="animate-pulse text-sm text-blue-600">
              Please check your phone for USSD prompt...
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

interface Price {
  id: string;
  name: string;
  amount: number;
  currency: string;
}

interface Event {
  id: string;
  name: string;
  description: string;
  prices: Price[];
  location?: string;
  date?: string;
  time?: string;
}

export default function EventDetailsPage() {
  const { eventId } = useParams<{ eventId: string }>();
  const router = useRouter();

  const [event, setEvent] = useState<Event | null>(null);
  const [selectedPriceId, setSelectedPriceId] = useState<string>("");
  const [selectedPrice, setSelectedPrice] = useState<Price | null>(null);
  const [ownerName, setOwnerName] = useState<string>("");
  const [ownerEmail, setOwnerEmail] = useState<string>("");
  const [msisdn, setMsisdn] = useState<string>("");
  const [tickets, setTickets] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [paymentStatus, setPaymentStatus] = useState<
    "loading" | "success" | "error" | null
  >(null);
  const [paymentMessage, setPaymentMessage] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [ticketData, setTicketData] = useState<any>(null);

  useEffect(() => {
    const fetchEvent = async () => {
      try {
        console.log("[v0] Fetching event details for ID:", eventId);
        const res = await fetch(
          `http://***********:8070/eventus/events/${eventId}`
        );

        if (!res.ok) {
          throw new Error(
            `Failed to fetch event: ${res.status} ${res.statusText}`
          );
        }

        const data = await res.json();
        console.log("[v0] Event details response:", data);
        setEvent(data.body);
      } catch (err: any) {
        console.error("[v0] Error fetching event:", err);
        setError("Failed to fetch event details");
      }
    };
    if (eventId) fetchEvent();
  }, [eventId]);

  const handleSelectTicket = (priceId: string) => {
    console.log("[v0] Selected ticket type:", priceId);
    setSelectedPriceId(priceId);
    const price = event?.prices.find((p) => p.id === priceId) || null;
    setSelectedPrice(price);
  };

  const pollStatus = async (transactionId: string) => {
    console.log(
      "[v0] Starting 30-second polling for transactionId:",
      transactionId
    );

    let attempts = 0;
    const maxAttempts = 45; // 15 attempts over 30 seconds
    const intervalTime = 2000; // 2 second intervals for 30-second total

    return new Promise<void>((resolve) => {
      const interval = setInterval(async () => {
        attempts++;
        console.log(
          `[v0] Polling attempt ${attempts}/${maxAttempts} for transaction:`,
          transactionId
        );

        try {
          // Use the new endpoint with eventId and msisdn
          console.log(
            "[v0] Polling URL:",
            `http://***********:8070/eventus/events/${eventId}/transactions/${msisdn}`
          );
          const res = await axios.get(
            `http://***********:8070/eventus/events/${eventId}/transactions/${msisdn}`,
            {
              timeout: 8000,
              headers: {
                Accept: "application/json",
                "Content-Type": "application/json",
              },
              params: {
                _t: Date.now(),
              },
            }
          );

          console.log("[v0] Raw polling response:", res);
          console.log("[v0] Response status:", res.status);
          console.log("[v0] Response data:", res.data);

          const responseData = res.data?.body || res.data;

          // Find the specific transaction by ID from the array of transactions
          let targetTransaction = null;
          if (Array.isArray(responseData)) {
            targetTransaction = responseData.find(
              (transaction: any) => transaction.id === transactionId
            );
          }

          if (!targetTransaction) {
            console.log(
              "[v0] Transaction not found in response, continuing to poll..."
            );
            setPaymentMessage(
              "⏳ Waiting for transaction to be registered... Please complete USSD payment"
            );
            return;
          }

          const status = targetTransaction?.status;
          const msg = targetTransaction?.apiResponseMsg || "";

          console.log("[v0] Found target transaction:", targetTransaction);
          console.log("[v0] Detected status:", status);

          if (status === "SUCCESSFUL") {
            clearInterval(interval);
            console.log("[v0] Payment confirmed as successful!");
            setPaymentStatus("success");
            setPaymentMessage(
              "🎉 Payment successful! Redirecting to your ticket..."
            );

            setTimeout(() => {
              const params = new URLSearchParams({
                transactionId: transactionId,
                msisdn: msisdn,
              });
              router.push(
                `/services/events/${eventId}/success?${params.toString()}`
              );
            }, 2000);
            resolve();
            return;
          } else if (status === "FAILED") {
            clearInterval(interval);
            setPaymentStatus("error");
            setPaymentMessage(msg || "❌ Payment failed. Please try again.");
            resolve();
            return;
          } else if (status === "PROCESSING") {
            setPaymentMessage(
              "⏳ Payment is being processed... Please check your phone for USSD prompt"
            );
          } else {
            setPaymentMessage(
              `⏳ Processing payment... Please confirm on your phone (Status: ${
                status || "checking"
              })`
            );
          }
        } catch (err: any) {
          console.error("[v0] Polling error:", err);
          console.error("[v0] Error response:", err.response?.data);
          console.error("[v0] Error status:", err.response?.status);

          if (err.response?.status === 404) {
            console.warn("[v0] Transaction not found, continuing to poll...");
            setPaymentMessage(
              "⏳ Waiting for transaction to be registered... Please complete USSD payment"
            );
          } else if (err.response?.status >= 500) {
            setPaymentMessage("⏳ Server temporarily unavailable, retrying...");
          } else if (
            err.code === "ECONNABORTED" ||
            err.message.includes("timeout")
          ) {
            setPaymentMessage("⏳ Network timeout, retrying...");
          } else if (err.response?.status === 0 || err.code === "ERR_NETWORK") {
            setPaymentMessage("⏳ Network connection issue, retrying...");
          } else {
            setPaymentMessage(
              "⏳ Checking payment status... Please complete your USSD payment"
            );
          }
        }

        if (attempts >= maxAttempts) {
          clearInterval(interval);
          console.log("[v0] Polling timeout after 30 seconds");

          setPaymentStatus("error");
          setPaymentMessage(
            "⏰ Payment confirmation timeout. If you completed the USSD payment, your ticket may still be processed. " +
              "Please check your transaction history or contact support if needed."
          );
          resolve();
        }
      }, intervalTime);
    });
  };

  const handlePayment = async () => {
    if (!selectedPriceId || !msisdn || !ownerName || !ownerEmail) {
      setError("Please fill in all fields and select a ticket type");
      return;
    }

    if (!/^7[1-9]\d{7}$/.test(msisdn)) {
      setError("Please enter a valid mobile number (e.g., 712000000)");
      return;
    }

    if (!/\S+@\S+\.\S+/.test(ownerEmail)) {
      setError("Please enter a valid email address");
      return;
    }

    setError(null);
    setLoading(true);
    setModalOpen(true);
    setPaymentStatus("loading");
    setPaymentMessage(
      "🚀 Initiating payment... Please check your phone for USSD prompt"
    );

    try {
      const payload = {
        ownerName,
        ownerEmail,
        msisdn: msisdn,
        eventId,
        priceId: selectedPriceId,
        numberOfTickets: tickets,
      };

      console.log("[v0] Sending enhanced payment request:", payload);

      const res = await axios.post(
        "http://***********:8070/eventus/events/transaction/pay",
        payload,
        {
          timeout: 20000,
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      console.log("[v0] Payment API response:", res.data);

      const transactionId =
        res.data?.body?.transactionId ||
        res.data?.transactionId ||
        res.data?.body?.id;

      if ((res.data?.success || res.status === 200) && transactionId) {
        console.log(
          "[v0] Payment initiated successfully with transactionId:",
          transactionId
        );
        setPaymentMessage(
          "✅ Payment initiated successfully! Waiting for confirmation..."
        );
        await pollStatus(transactionId);
      } else {
        setPaymentStatus("error");
        setPaymentMessage(
          res.data?.message ||
            res.data?.body?.message ||
            "❌ Payment initiation failed"
        );
      }
    } catch (err: any) {
      console.error("[v0] Payment error:", err.response?.data || err.message);
      setPaymentStatus("error");
      setPaymentMessage(
        err.response?.data?.message ||
          err.response?.data?.body?.message ||
          `❌ Payment failed (${
            err.response?.status || "network error"
          }). Please try again.`
      );
    } finally {
      setLoading(false);
    }
  };

  if (!event) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#f68c1e] mx-auto mb-4"></div>
        <p className="text-gray-500 text-lg">Loading event details...</p>
      </div>
    );
  }

  const total =
    selectedPrice && tickets > 0
      ? (selectedPrice.amount * tickets).toFixed(2)
      : null;

  return (
    <div className="max-w-2xl mx-auto p-6">
      <PaymentModal
        isOpen={modalOpen}
        status={paymentStatus}
        message={paymentMessage}
      />

      <Card className="shadow-lg">
        <CardHeader className="bg-gradient-to-r from-[#f68c1e] to-[#ff6b00] text-white">
          <CardTitle className="text-2xl font-bold">{event.name}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 p-6">
          <p className="text-gray-600 leading-relaxed">{event.description}</p>

          <div>
            <Label className="block mb-3 text-lg font-semibold">
              Select Ticket Type
            </Label>
            <Select onValueChange={handleSelectTicket}>
              <SelectTrigger className="h-12">
                <SelectValue placeholder="Choose a ticket type" />
              </SelectTrigger>
              <SelectContent>
                {event.prices.map((price) => (
                  <SelectItem key={price.id} value={price.id}>
                    {price.name} - ${price.amount} {price.currency}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="ownerName" className="text-sm font-medium">
                Your Name *
              </Label>
              <Input
                id="ownerName"
                type="text"
                placeholder="John Doe"
                value={ownerName}
                onChange={(e) => setOwnerName(e.target.value)}
                className="h-12 mt-1"
              />
            </div>

            <div>
              <Label htmlFor="ownerEmail" className="text-sm font-medium">
                Your Email *
              </Label>
              <Input
                id="ownerEmail"
                type="email"
                placeholder="<EMAIL>"
                value={ownerEmail}
                onChange={(e) => setOwnerEmail(e.target.value)}
                className="h-12 mt-1"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="tickets" className="text-sm font-medium">
              Number of Tickets
            </Label>
            <Input
              id="tickets"
              type="number"
              value={tickets}
              onChange={(e) => setTickets(Math.max(1, Number(e.target.value)))}
              min={1}
              max={10}
              className="h-12 mt-1"
            />
          </div>

          {total && (
            <div className="p-4 bg-gradient-to-r from-[#f68c1e]/10 to-[#ff6b00]/10 rounded-lg border-2 border-[#f68c1e]/20">
              <div className="text-center">
                <p className="text-sm text-gray-600">Total Amount</p>
                <p className="text-2xl font-bold text-[#f68c1e]">
                  ${total} {selectedPrice?.currency}
                </p>
              </div>
            </div>
          )}

          <div>
            <Label htmlFor="msisdn" className="text-sm font-medium">
              OneMoney Mobile Number *
            </Label>
            <div className="flex mt-1">
              <span className="inline-flex items-center px-4 bg-gray-100 border border-r-0 border-gray-300 rounded-l-md text-gray-700 font-medium">
                +263
              </span>
              <Input
                id="msisdn"
                type="text"
                placeholder="712000000"
                value={msisdn}
                onChange={(e) => setMsisdn(e.target.value.replace(/^0+/, ""))}
                className="rounded-l-none flex-1 h-12"
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Enter your mobile number for payment confirmation
            </p>
          </div>

          <Button
            onClick={handlePayment}
            disabled={
              loading ||
              !selectedPriceId ||
              !ownerName ||
              !ownerEmail ||
              !msisdn
            }
            className="w-full bg-[#f68c1e] hover:bg-[#e07c1a] h-12 text-lg font-semibold transition-all duration-200">
            {loading ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Processing Payment...
              </>
            ) : (
              `Pay Now - $${total || "0.00"}`
            )}
          </Button>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600 text-sm font-medium">{error}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
