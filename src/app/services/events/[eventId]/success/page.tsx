"use client";

import { useEffect, useState } from "react";
import { useSearchParams, useParams } from "next/navigation";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardContent,
} from "../../../../../components/ui/card";
import { Button } from "../../../../../components/ui/button";
import {
  CheckCircle2,
  Download,
  Calendar,
  Ticket,
  Hash,
  AlertCircle,
} from "lucide-react";
import { EventTicketCard } from "@/components/EventTicketCard";
import { instanceEvents } from "../../../../../utils/axios";

interface Ticket {
  ticketId: string;
  ownerName: string;
  ownerEmail: string;
  active: boolean;
  ticketStatus: string;
  price: {
    id: string;
    name: string;
    eventId: string;
    currency: string;
    amount: number;
    active: boolean;
  };
  extraDetails: any;
  options: any;
  eventName: string;
  eventLocation: string;
}

interface TransactionData {
  tickets: Ticket[];
  status: string;
  currency: string;
  transactionId: string;
  amount: number;
}

interface EventData {
  id: string;
  name: string;
  description: string;
  date: string;
  startTime: string;
  endTime: string;
  location: string;
  prices: any[];
}

export default function PaymentSuccessPage() {
  const searchParams = useSearchParams();
  const params = useParams();
  const eventId = params.eventId as string;

  const [transactionData, setTransactionData] =
    useState<TransactionData | null>(null);
  const [eventData, setEventData] = useState<EventData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get transaction ID and msisdn from URL parameters
        const transactionId = searchParams.get("transactionId");
        const msisdn = searchParams.get("msisdn");

        if (!transactionId || !msisdn) {
          throw new Error("Missing transaction ID or phone number");
        }

        // Fetch transaction data
        console.log("Fetching transaction data...");
        const transactionResponse = await instanceEvents.get(
          `/events/${eventId}/transactions/${msisdn}`
        );

        if (!transactionResponse.data.success) {
          throw new Error(
            transactionResponse.data.message ||
              "Failed to fetch transaction data"
          );
        }

        const transactionData =
          transactionResponse.data.body[
            transactionResponse.data.body.length - 1
          ];

        setTransactionData(transactionData);

        // Fetch event details
        console.log("Fetching event details...");
        const eventResponse = await instanceEvents.get(`/events/${eventId}`);

        if (!eventResponse.data.success) {
          throw new Error(
            eventResponse.data.message || "Failed to fetch event details"
          );
        }

        const eventData = eventResponse.data.body;
        setEventData(eventData);
      } catch (err: any) {
        console.error("Error fetching data:", err);
        setError(err.message || "Failed to load ticket data");
      } finally {
        setLoading(false);
      }
    };

    if (eventId) {
      fetchData();
    }
  }, [eventId, searchParams]);

  const downloadAllTickets = async () => {
    if (!transactionData || !eventData) return;

    try {
      // Create a zip file or download individual tickets
      for (let i = 0; i < transactionData.tickets.length; i++) {
        const ticket = transactionData.tickets[i];
        await downloadSingleTicket(ticket, i);
        // Add a small delay between downloads
        if (i < transactionData.tickets.length - 1) {
          await new Promise((resolve) => setTimeout(resolve, 500));
        }
      }
    } catch (error) {
      console.error("Download failed:", error);
    }
  };

  const downloadSingleTicket = async (ticket: Ticket, index: number) => {
    if (!transactionData) return;

    try {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      // Set canvas size for ticket design
      canvas.width = 800;
      canvas.height = 600;

      // White background
      ctx.fillStyle = "#ffffff";
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Orange header
      ctx.fillStyle = "#f68c1e";
      ctx.fillRect(0, 0, canvas.width, 100);

      // Event name
      ctx.fillStyle = "#ffffff";
      ctx.font = "bold 28px Arial";
      ctx.textAlign = "center";
      ctx.fillText(ticket.eventName, canvas.width / 2, 40);

      ctx.font = "16px Arial";
      ctx.fillText("EVENT TICKET", canvas.width / 2, 70);

      // Ticket details
      ctx.fillStyle = "#333333";
      ctx.font = "18px Arial";
      ctx.textAlign = "left";

      const details = [
        `Event: ${ticket.eventName}`,
        `Location: ${ticket.eventLocation}`,
        `Date: ${
          eventData?.date
            ? new Date(eventData.date).toLocaleDateString()
            : "TBD"
        }`,
        `Time: ${eventData?.startTime || "TBD"} - ${
          eventData?.endTime || "TBD"
        }`,
        `Holder: ${ticket.ownerName}`,
        `Email: ${ticket.ownerEmail}`,
        `Type: ${ticket.price.name}`,
        `Price: ${ticket.price.currency} ${ticket.price.amount.toFixed(2)}`,
        `Transaction: ${transactionData.transactionId}`,
        `Ticket ID: ${ticket.ticketId}`,
      ];

      details.forEach((detail, index) => {
        ctx.fillText(detail, 50, 140 + index * 25);
      });

      // QR Code area
      const qrSize = 120;
      const qrX = canvas.width - qrSize - 50;
      const qrY = 140;

      // QR border
      ctx.strokeStyle = "#333333";
      ctx.lineWidth = 2;
      ctx.strokeRect(qrX - 10, qrY - 10, qrSize + 20, qrSize + 20);

      // Simple QR pattern (placeholder)
      const cellSize = 4;
      const cells = Math.floor(qrSize / cellSize);

      for (let i = 0; i < cells; i++) {
        for (let j = 0; j < cells; j++) {
          const hash = (
            ticket.ticketId + transactionData.transactionId
          ).charCodeAt(
            (i * cells + j) %
              (ticket.ticketId + transactionData.transactionId).length
          );
          if (hash % 2 === 0) {
            ctx.fillStyle = "#000000";
            ctx.fillRect(
              qrX + i * cellSize,
              qrY + j * cellSize,
              cellSize,
              cellSize
            );
          }
        }
      }

      // Download the image
      const link = document.createElement("a");
      link.download = `${ticket.eventName.replace(/\s+/g, "_")}-ticket-${
        index + 1
      }-${ticket.ticketId.slice(-8)}.png`;
      link.href = canvas.toDataURL();
      link.click();
    } catch (error) {
      console.error("Single ticket download failed:", error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#f68c1e] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your tickets...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-50">
        <div className="text-center max-w-md mx-auto px-4">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-red-100 rounded-full mb-4">
            <AlertCircle className="w-12 h-12 text-red-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Error Loading Tickets
          </h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button
            onClick={() => window.location.reload()}
            className="bg-[#f68c1e] hover:bg-[#e07c1a]">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  if (!transactionData || !eventData) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="text-center">
          <p className="text-gray-600">No ticket data found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-4">
            <CheckCircle2 className="w-12 h-12 text-green-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Payment Successful!
          </h1>
          <p className="text-xl text-gray-600">
            Your {transactionData.tickets.length} ticket
            {transactionData.tickets.length > 1 ? "s have" : " has"} been
            generated and {transactionData.tickets.length > 1 ? "are" : "is"}{" "}
            ready for download.
          </p>
          <div className="inline-flex items-center gap-2 mt-4 px-4 py-2 bg-green-100 rounded-full">
            <CheckCircle2 className="w-5 h-5 text-green-600" />
            <span className="text-green-700 font-medium">
              Transaction Status: {transactionData.status}
            </span>
          </div>
        </div>

        {/* Transaction Summary */}
        <Card className="mb-8 shadow-xl">
          <CardHeader className="bg-gradient-to-r from-[#f68c1e] to-[#ff6b00] text-white">
            <CardTitle className="text-2xl font-bold text-center">
              Transaction Summary
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Calendar className="w-5 h-5 text-[#f68c1e]" />
                  <span className="font-semibold">Event</span>
                </div>
                <p className="text-lg font-bold text-gray-900">
                  {eventData.name}
                </p>
                <p className="text-sm text-gray-600">{eventData.location}</p>
                {eventData.date && (
                  <p className="text-sm text-gray-600">
                    {new Date(eventData.date).toLocaleDateString()}
                  </p>
                )}
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Ticket className="w-5 h-5 text-[#f68c1e]" />
                  <span className="font-semibold">Tickets</span>
                </div>
                <p className="text-lg font-bold text-gray-900">
                  {transactionData.tickets.length} Ticket
                  {transactionData.tickets.length > 1 ? "s" : ""}
                </p>
                <p className="text-sm text-gray-600">
                  {transactionData.tickets[0]?.price.name || "Standard"}
                </p>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Hash className="w-5 h-5 text-[#f68c1e]" />
                  <span className="font-semibold">Total Paid</span>
                </div>
                <p className="text-lg font-bold text-[#f68c1e]">
                  {transactionData.currency} {transactionData.amount.toFixed(2)}
                </p>
                <p className="text-xs text-gray-500 font-mono">
                  {transactionData.transactionId}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tickets Display */}
        <div className="space-y-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              Your Tickets
            </h2>
            <p className="text-gray-600">
              {transactionData.tickets.length} ticket
              {transactionData.tickets.length > 1 ? "s" : ""} ready for download
            </p>
          </div>

          {transactionData.tickets.map((ticket, index) => (
            <div key={ticket.ticketId} className="flex justify-center">
              <EventTicketCard
                ticket={ticket}
                event={eventData}
                transactionId={transactionData.transactionId}
                onDownload={() => downloadSingleTicket(ticket, index)}
              />
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 mt-8 justify-center">
          <Button
            onClick={downloadAllTickets}
            className="bg-[#f68c1e] hover:bg-[#e07c1a] text-white px-8 py-3 text-lg font-semibold">
            <Download className="w-5 h-5 mr-2" />
            Download All Tickets
          </Button>

          <Button
            onClick={() => window.print()}
            variant="outline"
            className="border-[#f68c1e] text-[#f68c1e] hover:bg-[#f68c1e] hover:text-white px-8 py-3 text-lg font-semibold">
            Print Tickets
          </Button>
        </div>

        {/* Instructions */}
        <Card className="mt-8 shadow-lg">
          <CardContent className="p-6 bg-blue-50 border border-blue-200">
            <h3 className="font-semibold text-blue-900 mb-4 text-lg">
              Important Instructions:
            </h3>
            <ul className="text-blue-800 space-y-2 text-sm">
              <li className="flex items-start gap-2">
                <span className="text-[#f68c1e] font-bold">•</span>
                Present your ticket (digital or printed) at the event entrance
              </li>
              <li className="flex items-start gap-2">
                <span className="text-[#f68c1e] font-bold">•</span>
                The QR code will be scanned for verification
              </li>
              <li className="flex items-start gap-2">
                <span className="text-[#f68c1e] font-bold">•</span>
                Keep your transaction ID safe for reference:{" "}
                <code className="bg-white px-2 py-1 rounded font-mono text-xs">
                  {transactionData.transactionId}
                </code>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-[#f68c1e] font-bold">•</span>
                Arrive at least 30 minutes before the event starts
              </li>
              <li className="flex items-start gap-2">
                <span className="text-[#f68c1e] font-bold">•</span>
                Contact support if you have any issues: <EMAIL>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
