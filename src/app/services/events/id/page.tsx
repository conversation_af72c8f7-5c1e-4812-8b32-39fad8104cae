import { EventBookingForm } from "@/components/event-booking-form"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import Footer from "@/components/Footer"
import { Calendar, MapPin, Users, Clock, ArrowLeft } from "lucide-react"
import { Badge } from "@/components/ui/badge"

// Sample events data - this would typically come from an API
const events = [
  {
    id: 1,
    title: "Harare International Music Festival",
    description:
      "Join us for an amazing night of music featuring local and international artists. Experience the best of Zimbabwean and international music in one spectacular event.",
    date: "2024-02-15",
    time: "18:00",
    venue: "National Sports Stadium",
    location: "Harare, Zimbabwe",
    price: 25.0,
    currency: "USD",
    availableTickets: 500,
    category: "Music",
    image: "/music-festival-concert-stage.png",
    fullDescription:
      "The Harare International Music Festival is back with an incredible lineup of local and international artists. This year's festival promises to be the biggest yet, featuring multiple stages, food vendors, and an unforgettable atmosphere. Don't miss out on this spectacular celebration of music and culture.",
  },
  {
    id: 2,
    title: "Zimbabwe Business Summit 2024",
    description: "Network with business leaders and learn about the latest trends in entrepreneurship.",
    date: "2024-02-20",
    time: "09:00",
    venue: "Rainbow Towers Hotel",
    location: "Harare, Zimbabwe",
    price: 50.0,
    currency: "USD",
    availableTickets: 200,
    category: "Business",
    image: "/business-conference-meeting.png",
    fullDescription:
      "Join Zimbabwe's premier business summit featuring keynote speakers, networking sessions, and workshops designed to help entrepreneurs and business leaders thrive in today's competitive market.",
  },
  {
    id: 3,
    title: "Comedy Night with Local Stars",
    description: "Laugh out loud with Zimbabwe's funniest comedians in one unforgettable night.",
    date: "2024-02-25",
    time: "19:30",
    venue: "7 Arts Theatre",
    location: "Avondale, Harare",
    price: 15.0,
    currency: "USD",
    availableTickets: 150,
    category: "Comedy",
    image: "/comedy-show-theater-stage.png",
    fullDescription:
      "Get ready for a night of non-stop laughter with Zimbabwe's most talented comedians. This comedy show features the best local talent performing their latest material in an intimate theater setting.",
  },
  {
    id: 4,
    title: "Tech Innovation Conference",
    description: "Discover the latest in technology and innovation with industry experts.",
    date: "2024-03-05",
    time: "08:30",
    venue: "University of Zimbabwe",
    location: "Mount Pleasant, Harare",
    price: 30.0,
    currency: "USD",
    availableTickets: 300,
    category: "Technology",
    image: "/technology-conference-innovation.png",
    fullDescription:
      "Explore the future of technology in Zimbabwe and Africa. This conference brings together tech leaders, innovators, and entrepreneurs to discuss emerging trends and opportunities in the digital economy.",
  },
  {
    id: 5,
    title: "Cultural Heritage Festival",
    description: "Celebrate Zimbabwe's rich cultural heritage with traditional music, dance, and food.",
    date: "2024-03-10",
    time: "10:00",
    venue: "Harare Gardens",
    location: "Central Harare",
    price: 10.0,
    currency: "USD",
    availableTickets: 1000,
    category: "Culture",
    image: "/cultural-festival-traditional-dance.png",
    fullDescription:
      "Immerse yourself in Zimbabwe's rich cultural heritage through traditional music, dance performances, art exhibitions, and authentic local cuisine. A perfect family-friendly event celebrating our diverse cultural traditions.",
  },
  {
    id: 6,
    title: "Food & Wine Expo",
    description: "Taste the best of Zimbabwe's cuisine and wines from local producers.",
    date: "2024-03-15",
    time: "12:00",
    venue: "Harare International Conference Centre",
    location: "Harare, Zimbabwe",
    price: 20.0,
    currency: "USD",
    availableTickets: 400,
    category: "Food",
    image: "/food-wine-expo-tasting.png",
    fullDescription:
      "Discover the flavors of Zimbabwe at this premier food and wine expo. Sample dishes from top local chefs, taste wines from emerging vineyards, and learn about sustainable food production in Zimbabwe.",
  },
]

interface EventPageProps {
  params: {
    id: string
  }
}

export default function EventPage({ params }: EventPageProps) {
  const eventId = Number.parseInt(params.id)
  const event = events.find((e) => e.id === eventId)

  if (!event) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Event Not Found</h1>
          <Link href="/services/events">
            <Button className="bg-[#f68c1e] hover:bg-[#e07c1a]">Back to Events</Button>
          </Link>
        </div>
      </div>
    )
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <header className="bg-gradient-to-r from-[#f68c1e] to-[#ff6b00] text-white p-6 shadow-lg">
        <div className="container mx-auto flex items-center justify-between">
          <Link href="/services/events" className="flex items-center space-x-3">
            <ArrowLeft className="w-5 h-5" />
            <h1 className="text-2xl font-bold tracking-tight">Event Booking</h1>
          </Link>
          <Link href="/">
            <Button variant="outline" className="bg-white text-[#f68c1e] hover:bg-gray-100">
              Back to Home
            </Button>
          </Link>
        </div>
      </header>

      <main className="container mx-auto py-12">
        <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Event Details */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="relative">
              <img src={event.image || "/placeholder.svg"} alt={event.title} className="w-full h-64 object-cover" />
              <Badge className="absolute top-4 right-4 bg-[#f68c1e] text-white text-sm px-3 py-1">
                {event.category}
              </Badge>
            </div>

            <div className="p-6">
              <h1 className="text-2xl font-bold text-gray-800 mb-4">{event.title}</h1>

              <div className="space-y-3 mb-6">
                <div className="flex items-center gap-3 text-gray-600">
                  <Calendar className="w-5 h-5 text-[#f68c1e]" />
                  <span>{formatDate(event.date)}</span>
                </div>

                <div className="flex items-center gap-3 text-gray-600">
                  <Clock className="w-5 h-5 text-[#f68c1e]" />
                  <span>{event.time}</span>
                </div>

                <div className="flex items-center gap-3 text-gray-600">
                  <MapPin className="w-5 h-5 text-[#f68c1e]" />
                  <span>
                    {event.venue}, {event.location}
                  </span>
                </div>

                <div className="flex items-center gap-3 text-gray-600">
                  <Users className="w-5 h-5 text-[#f68c1e]" />
                  <span>{event.availableTickets} tickets available</span>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-2">About This Event</h3>
                <p className="text-gray-600 leading-relaxed">{event.fullDescription}</p>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <span className="text-lg font-semibold text-gray-800">Ticket Price:</span>
                  <span className="text-2xl font-bold text-[#f68c1e]">
                    ${event.price.toFixed(2)} {event.currency}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Booking Form */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="bg-gradient-to-br from-[#f68c1e] to-[#ff6b00] p-6">
              <h2 className="text-2xl font-semibold text-center text-white mb-2">Book Your Tickets</h2>
              <p className="text-center text-white/90">Secure your spot at this amazing event</p>
            </div>
            <div className="p-6">
              <EventBookingForm event={event} />
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
