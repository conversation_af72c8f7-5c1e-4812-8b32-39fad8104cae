import { ElectricityForm } from "@/components/electricity-form"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import Footer from "@/components/Footer"
import zesaIcon from "../../../assets/images/zesa.png"
import Image from "next/image"

export default function ElectricityPage() {
    return (
        <div className="min-h-screen bg-gray-100">
            <header className="bg-gradient-to-r from-[#f68c1e] to-[#ff6b00] text-white p-6 shadow-lg">
                <div className="container mx-auto flex items-center justify-between">
                    <Link href="/" className="flex items-center space-x-3">

                        <h1 className="text-2xl font-bold tracking-tight">Electricity Service</h1>
                    </Link>
                    <Link href="/">
                        <Button variant="outline" className="bg-white text-[#f68c1e] hover:bg-gray-100">
                            Back to Home
                        </Button>
                    </Link>
                </div>
            </header>

            <main className="container mx-auto py-12">
                <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
                    <div className="bg-gradient-to-br from-[#f68c1e] to-[#ff6b00] p-6">
                        <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
                            {/* <Electricity className="w-10 h-10 text-[#f68c1e]" /> */}
                            <Image src={zesaIcon} alt="zesa" />
                        </div>
                    </div>
                    <div className="p-6">
                        <h2 className="text-2xl font-semibold text-center mb-6 text-gray-800">Pay Your Electricity Bill</h2>
                        <ElectricityForm />
                    </div>
                </div>
            </main>

            <Footer />
        </div>
    )
}

