const publicKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlLxdNwS6z7zaATdvfEUB
Q8z9CTAaZAvfZPDjzx5o+xXWzAC2qyVdTM/zFeaJ5/ocC5hTnAYph9ZKUPE3znPW
dV6+peJhp/jFTPifVFD8SE3Y8MWxRavWTlUEnY76wrsOrgocZcrLd5coDF3tCUX8
6GKMLt+4hI0JNt06I79iqC4Zq/eWGL/7gR6mSJ2ADcWbdaLoxzH/jBBb1tKAHC9Q
eGl4qrK80YHnB/83RTeMquxeVDgtZlbTABZh9DQGsM3igH1SRZyzpPL6UoBFmnU6
o7KogH0tBq472C3j2x5YW4ox4EnlIDy6w/Y0kyQhLZq9m8UJSeIEzZgZV3sKhO2e
PwIDAQAB
-----END PUBLIC KEY-----`;
function base64ToArrayBuffer(base64: string) {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes.buffer;
}

function arrayBufferToBase64(buffer: ArrayBuffer) {
  let binary = "";
  const bytes = new Uint8Array(buffer);
  const len = bytes.byteLength;
  for (let i = 0; i < len; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
}

function publicPemToBuffer(pem: string) {
  const b64Lines = pem
    .replace("-----BEGIN PUBLIC KEY-----", "")
    .replace("-----END PUBLIC KEY-----", "")
    .replace(/\n/g, "");
  return base64ToArrayBuffer(b64Lines);
}

async function importPublicKey(pem: string) {
  const keyBuffer = publicPemToBuffer(pem);
  return await window.crypto.subtle.importKey(
    "spki",
    keyBuffer,
    {
      name: "RSA-OAEP",
      hash: "SHA-256",
    },
    true,
    ["encrypt"]
  );
}

async function encryptString(plainText: string) {
  const importedPublicKey = await importPublicKey(publicKey);
  const encoded = new TextEncoder().encode(plainText);
  const encrypted = await window.crypto.subtle.encrypt(
    {
      name: "RSA-OAEP",
    },
    importedPublicKey,
    encoded
  );
  return arrayBufferToBase64(encrypted);
}

export default encryptString;
