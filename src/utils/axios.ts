import axios from "axios"
export const baseURL = "http://10.95.10.208:8710/api/v1/digital-products"
export const baseURL2 = "https://shop.onemoney.co.zw/api"
export const baseURLtest = "http://10.95.10.172:9002"
export const baseURLEvents = "http://10.95.119.4:8070/eventus"

const instance = axios.create({
  baseURL: baseURL,
})

export const instance2 = axios.create({
  baseURL: baseURL2,
})

export const instancetest = axios.create({
  baseURL: baseURLtest,
})

export const instanceEvents = axios.create({
  baseURL: baseURLEvents,
})

export default instance
