import axios from "../../utils/axios"
import { instancetest } from "../../utils/axios"

interface EventBookingData {
  eventId: number
  customerName: string
  email: string
  mobileNumber: string
  ticketQuantity: number
  totalAmount: number
  currency: string
}

interface EventLookup {
  eventId: number
}

interface PaymentData {
  payload: string
}

interface ConfirmationData {
  otp: string
}

class EventsService {
  async bookTicket(data: EventBookingData) {
    return axios.post(`/buy/events/book`, data, {
      headers: {
        "Content-type": "application/json",
      },
    })
  }

  async pay(data: PaymentData) {
    return axios.post(`/buy/events/auth`, data, {
      headers: {
        "Content-type": "application/json",
      },
    })
  }

  async confirm(data: ConfirmationData) {
    return axios.post(`/buy/events`, data, {
      headers: {
        "Content-type": "application/json",
      },
    })
  }

  async getEventDetails(data: EventLookup) {
    return instancetest.get(`/events/v1/details/${data.eventId}`, {
      headers: {
        "Content-type": "application/json",
      },
    })
  }

  async getAvailableEvents() {
    return instancetest.get(`/events/v1/available`, {
      headers: {
        "Content-type": "application/json",
      },
    })
  }

  async checkTicketAvailability(data: EventLookup) {
    return instancetest.get(`/events/v1/availability/${data.eventId}`, {
      headers: {
        "Content-type": "application/json",
      },
    })
  }
}

const eventsServiceInstance = new EventsService()
export default eventsServiceInstance
