import axios from "../../utils/axios";
import { instancetest } from "../../utils/axios";

interface lookup {
  meterNumber: string;
}

class ZesaService {
  async pay(data: object) {
    return axios.post(`/buy/zesa/auth`, data, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }
  async confirm(data: object) {
    return axios.post(`/buy/zesa`, data, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }
  async lookUpCustomer(data: lookup) {
    return instancetest.get(`/zesa/v1/customer/${data.meterNumber}`, {
      headers: {
        "Content-type": "application/json",
      },
    });
  }
}

const zesaServiceInstance = new ZesaService();
export default zesaServiceInstance;